/**
 * Template de e-mail para alertas administrativos
 * Tom profissional e informativo para gestores
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface AdminAlertTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados do administrador
  adminName: string;
  
  // Dados do alerta
  alertType: 'overdue_payments' | 'low_attendance' | 'system_issue' | 'financial_summary' | 'custom';
  alertTitle: string;
  alertMessage: string;
  
  // Dados específicos para atrasos
  overdueCount?: number;
  totalAtRisk?: number;
  currency?: string;
  
  // Dados específicos para frequência
  lowAttendanceCount?: number;
  attendanceThreshold?: number;
  
  // Configurações opcionais
  actionUrl?: string;
  actionLabel?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  additionalData?: Array<{
    label: string;
    value: string | number;
    highlight?: boolean;
  }>;
}

export function AdminAlertTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  adminName,
  alertType,
  alertTitle,
  alertMessage,
  overdueCount,
  totalAtRisk,
  currency = 'BRL',
  lowAttendanceCount,
  attendanceThreshold,
  actionUrl,
  actionLabel,
  priority = 'medium',
  additionalData
}: AdminAlertTemplateProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  // Configurações baseadas na prioridade
  const priorityConfig = {
    low: {
      color: '#059669',
      bgColor: '#f0fdf4',
      borderColor: '#bbf7d0',
      icon: '💡'
    },
    medium: {
      color: '#d97706',
      bgColor: '#fffbeb',
      borderColor: '#fed7aa',
      icon: '⚠️'
    },
    high: {
      color: '#dc2626',
      bgColor: '#fef2f2',
      borderColor: '#fecaca',
      icon: '🚨'
    },
    critical: {
      color: '#991b1b',
      bgColor: '#fef2f2',
      borderColor: '#f87171',
      icon: '🔥'
    }
  };

  const config = priorityConfig[priority];

  // Ícones específicos por tipo de alerta
  const typeIcons = {
    overdue_payments: '💰',
    low_attendance: '📉',
    system_issue: '⚙️',
    financial_summary: '📊',
    custom: '📋'
  };

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`Alerta Administrativo: ${alertTitle}`}
    >
      <EmailHeading level={1} color={config.color}>
        {config.icon} Alerta Administrativo
      </EmailHeading>

      <EmailText>
        Olá <strong>{adminName}</strong>,
      </EmailText>

      <EmailText>
        Este é um alerta automático do sistema de gestão da {academyName}.
      </EmailText>

      <EmailDivider color={config.color} />

      {/* Detalhes do alerta */}
      <div style={{
        backgroundColor: config.bgColor,
        padding: '24px',
        borderRadius: '8px',
        border: `2px solid ${config.borderColor}`
      }}>
        <EmailHeading level={2} color={config.color}>
          {typeIcons[alertType]} {alertTitle}
        </EmailHeading>

        <EmailText>
          {alertMessage}
        </EmailText>

        {/* Dados específicos para atrasos de pagamento */}
        {alertType === 'overdue_payments' && (overdueCount || totalAtRisk) && (
          <div style={{
            backgroundColor: '#fee2e2',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fca5a5',
            marginTop: '16px'
          }}>
            {overdueCount && (
              <EmailText variant="small" color="#dc2626">
                <strong>📊 Pagamentos em Atraso:</strong> {overdueCount} alunos
              </EmailText>
            )}
            {totalAtRisk && (
              <EmailText variant="small" color="#dc2626">
                <strong>💰 Valor Total em Risco:</strong> {formatCurrency(totalAtRisk)}
              </EmailText>
            )}
          </div>
        )}

        {/* Dados específicos para baixa frequência */}
        {alertType === 'low_attendance' && (lowAttendanceCount || attendanceThreshold) && (
          <div style={{
            backgroundColor: '#fef3c7',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fbbf24',
            marginTop: '16px'
          }}>
            {lowAttendanceCount && (
              <EmailText variant="small" color="#92400e">
                <strong>📉 Alunos com Baixa Frequência:</strong> {lowAttendanceCount} alunos
              </EmailText>
            )}
            {attendanceThreshold && (
              <EmailText variant="small" color="#92400e">
                <strong>📊 Limite de Frequência:</strong> {attendanceThreshold}% das aulas
              </EmailText>
            )}
          </div>
        )}

        {/* Dados adicionais */}
        {additionalData && additionalData.length > 0 && (
          <div style={{ marginTop: '20px' }}>
            <EmailHeading level={3} color={config.color}>
              📋 Detalhes Adicionais
            </EmailHeading>
            {additionalData.map((item, index) => (
              <EmailText 
                key={index} 
                variant="small" 
                color={item.highlight ? config.color : '#374151'}
              >
                <strong>{item.label}:</strong> {item.value}
              </EmailText>
            ))}
          </div>
        )}
      </div>

      <EmailDivider />

      {/* Recomendações baseadas no tipo */}
      <EmailHeading level={3} color={primaryColor}>
        💡 Ações Recomendadas
      </EmailHeading>

      {alertType === 'overdue_payments' && (
        <div style={{ marginBottom: '20px' }}>
          <EmailText variant="small">
            • Revisar lista de pagamentos em atraso no sistema
          </EmailText>
          <EmailText variant="small">
            • Entrar em contato com alunos em situação crítica
          </EmailText>
          <EmailText variant="small">
            • Considerar políticas de cobrança e negociação
          </EmailText>
        </div>
      )}

      {alertType === 'low_attendance' && (
        <div style={{ marginBottom: '20px' }}>
          <EmailText variant="small">
            • Verificar motivos da baixa frequência
          </EmailText>
          <EmailText variant="small">
            • Entrar em contato com alunos afetados
          </EmailText>
          <EmailText variant="small">
            • Revisar horários e modalidades oferecidas
          </EmailText>
        </div>
      )}

      {alertType === 'system_issue' && (
        <div style={{ marginBottom: '20px' }}>
          <EmailText variant="small">
            • Verificar logs do sistema para mais detalhes
          </EmailText>
          <EmailText variant="small">
            • Contatar suporte técnico se necessário
          </EmailText>
          <EmailText variant="small">
            • Monitorar impacto nas operações
          </EmailText>
        </div>
      )}

      {/* Botão de ação */}
      {actionUrl && actionLabel && (
        <div style={{ textAlign: 'center', margin: '32px 0' }}>
          <EmailButton href={actionUrl} primaryColor={config.color}>
            {actionLabel}
          </EmailButton>
        </div>
      )}

      {/* Informações de suporte */}
      <EmailText variant="small" color="#64748b">
        <strong>Precisa de ajuda?</strong> Este alerta foi gerado automaticamente 
        pelo sistema. Para mais informações ou suporte, acesse o painel administrativo.
      </EmailText>

      {priority === 'critical' && (
        <div style={{
          backgroundColor: '#fef2f2',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fca5a5',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#dc2626">
            <strong>⚠️ ATENÇÃO CRÍTICA:</strong> Este alerta requer ação imediata. 
            Verifique o sistema o quanto antes para evitar impactos nas operações.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
